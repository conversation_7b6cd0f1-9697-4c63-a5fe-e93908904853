import asyncio
from playwright.async_api import async_playwright
import re
import os
from datetime import datetime
from utils.fields import get_site_config
from utils.logger import get_logger
from utils.output import save_result_xlsx

# 需采集的字段
FIELDS = [
    '来源站点', '部门', '级别', '标题', '简介', '发布时间', '截止时间', '发文机关', '主题', '索引号', '文号',
    '详情地址', '正文内容', '敏感数字', '附件', '文件名及链接'
]

# 读取yaml配置
script_name = os.path.splitext(os.path.basename(__file__))[0]
site_info = get_site_config(script_name)
LIST_URL = site_info['url']
SOURCE_SITE = site_info.get('dept', site_info.get('来源站点', ''))
DEPT = site_info.get('dept', '')
LEVEL = site_info.get('level', '')

CONCURRENCY = 5  # 并发量可调

logger = get_logger(script_name)

async def fetch_all_detail_urls(page):
    detail_urls = []
    page_num = 1
    stop = False
    logger.info(f"开始采集列表页: {LIST_URL}")
    while not stop:
        await page.wait_for_selector('ul.clearfix.xxgk_nav_list li')
        items = await page.query_selector_all('ul.clearfix.xxgk_nav_list li')
        logger.info(f"第{page_num}页发现{len(items)}条记录")
        for item in items:
            li_text = (await item.inner_text()).strip()
            match = re.search(r'(20\d{2}-\d{2}-\d{2})', li_text)
            pub_date = match.group(1) if match else ''
            logger.debug(f'li_text: {li_text} | pub_date: {pub_date}')
            current_year = str(datetime.now().year)
            if not pub_date.startswith(current_year):
                logger.info(f"遇到非{current_year}年数据，终止翻页。")
                stop = True
                break
            a = await item.query_selector('a')
            href = await a.get_attribute('href') if a else None
            title = (await a.inner_text()).strip() if a else ''
            if href:
                detail_urls.append((page.url.rsplit('/', 1)[0] + '/' + href if not href.startswith('http') else href, title))
        if stop:
            break
        next_btn = await page.query_selector('a:has-text("下一页")')
        if next_btn and await next_btn.is_enabled():
            await next_btn.click()
            page_num += 1
            logger.info(f'翻到第{page_num}页...')
            await page.wait_for_timeout(1500)
        else:
            break
    logger.info(f"共采集到{len(detail_urls)}条详情页URL")
    return detail_urls

async def extract_detail(page, url_title):
    url, list_title = url_title if isinstance(url_title, tuple) else (url_title, '')
    try:
        logger.info(f"采集详情页: {url}")
        await page.goto(url)
        await page.wait_for_timeout(1000)
        data = {k: '' for k in FIELDS}
        data['详情地址'] = url
        data['来源站点'] = SOURCE_SITE
        data['部门'] = DEPT
        data['级别'] = LEVEL
        # 优先解析.table_suoyin表格内容
        suoyin_table = await page.query_selector('.table_suoyin')
        suoyin_data = {}
        if suoyin_table:
            rows = await suoyin_table.query_selector_all('tr')
            for row in rows:
                ths = await row.query_selector_all('th')
                tds = await row.query_selector_all('td')
                for i, th in enumerate(ths):
                    raw_key = (await th.inner_text()).strip()
                    key = re.sub(r'\s+', '', raw_key.replace(':', '').replace('：', '').replace('&emsp;', ''))
                    val = (await tds[i].inner_text()).strip() if i < len(tds) else ''
                    suoyin_data[key] = val
            # 字段映射（模糊匹配）
            for k, v in suoyin_data.items():
                if '索引号' in k:
                    data['索引号'] = v or '无'
                if '信息分类' in k:
                    data['主题'] = v or '无'
                if '发文机关' in k:
                    data['发文机关'] = v or '无'
                if '主题分类' in k:
                    data['主题'] = v or data['主题']
                if '成文日期' in k:
                    data['发布时间'] = v or '无'
                if '发布日期' in k:
                    data['发布时间'] = v or data['发布时间']
                if '发文字号' in k:
                    data['文号'] = v or '无'
                if '标题' in k or '名称' in k:
                    data['标题'] = v or '无'
        # 标题 优先用列表页a标签文本
        data['标题'] = list_title
        # 发布时间
        pub_elem = await page.query_selector('span.time, .pubtime, .date')
        if pub_elem:
            data['发布时间'] = (await pub_elem.inner_text()).strip()
        # 发文机关、主题、索引号、文号、截止时间
        info_elems = await page.query_selector_all('div.xxgk_info, .info, table tr')
        for elem in info_elems:
            text = (await elem.inner_text()).strip()
            for key in ['发文机关', '索引号', '文号', '截止时间']:
                if key in text:
                    value = text.split(key)[-1].strip(':： \n')
                    data[key] = value
            if '主题' in text or '主题分类' in text:
                value = text.split('主题')[-1].strip(':： \n') if '主题' in text else text.split('主题分类')[-1].strip(':： \n')
                data['主题'] = value if value else ''
        # 正文内容（gzk-article）
        content_elem = await page.query_selector('.gzk-article')
        if content_elem:
            data['正文内容'] = (await content_elem.inner_text()).strip() or '无'
        else:
            data['正文内容'] = '无'
        # 敏感数字
        if data['正文内容'] and data['正文内容'] != '无':
            sens = re.findall(r"[\d.]+\s*(?:元|%|％|万元|亿元)", data['正文内容'])
            data['敏感数字'] = ', '.join(sens) if sens else '无'
        else:
            data['敏感数字'] = '无'
        # 其余字段如无内容输出'无'
        for k in FIELDS:
            if not data[k]:
                data[k] = '无'
        # 附件（Excel超链接，顿号隔开）
        attach_elems = await page.query_selector_all('a[href]')
        attaches = []
        for a in attach_elems:
            href = await a.get_attribute('href')
            if href and any(x in href for x in ['.pdf', '.doc', '.xls', '.zip', '.rar']):
                if href.startswith('http'):
                    full_href = href
                else:
                    full_href = 'https://czt.ah.gov.cn/' + href.lstrip('/')
                name = (await a.inner_text()).strip()
                attaches.append(f'=HYPERLINK("{full_href}", "{name}")')
        data['附件'] = '\n'.join(attaches)
        # 文件名及链接：标题为Excel超链接
        if data['标题']:
            data['文件名及链接'] = f'=HYPERLINK("{url}", "{data["标题"]}")'
        logger.info(f"采集成功: {url}")
        return [data.get(f, '') for f in FIELDS]
    except Exception as e:
        logger.error(f'采集失败: {url}, 错误: {e}')
        logger.exception(e)
        return ['' for _ in FIELDS]

async def gather_with_concurrency(n, tasks):
    semaphore = asyncio.Semaphore(n)
    async def sem_task(task):
        async with semaphore:
            return await task
    return await asyncio.gather(*(sem_task(task) for task in tasks))

async def main():
    logger.info(f"启动采集任务: {script_name}, 入口: {LIST_URL}")
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.goto(LIST_URL)
        detail_urls = await fetch_all_detail_urls(page)
        # 并发抓取详情页
        tasks = []
        for url_title in detail_urls:
            detail_page = await browser.new_page()
            tasks.append(extract_detail(detail_page, url_title))
        results = await gather_with_concurrency(CONCURRENCY, tasks)
        logger.info(f"详情页采集完成，共{len(results)}条")
        out_path = save_result_xlsx(script_name, FIELDS, results)
        logger.info(f'采集完成，已输出到{out_path}')
        await browser.close()

if __name__ == '__main__':
    asyncio.run(main())
