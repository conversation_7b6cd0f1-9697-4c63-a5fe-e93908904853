import asyncio
from playwright.async_api import async_playwright
import re
import os
from utils.fields import get_site_config
from utils.logger import get_logger
from utils.output import save_result_xlsx
from bs4 import BeautifulSoup

FIELDS = [
    '来源站点', '部门', '级别', '标题', '简介', '发布时间', '截止时间', '发文机关', '主题', '索引号', '文号',
    '详情地址', '正文内容', '敏感数字', '附件', '文件名及链接'
]

# 字段映射规则（表头关键字到FIELDS）
FIELD_MAP = {
    '索引号': '索引号',
    '信息分类': '主题',
    '主题分类': '主题',
    '发文机关': '发文机关',
    '成文日期': '发布时间',
    '发布日期': '发布时间',
    '发文字号': '文号',
    '文号': '文号',
    '标题': '标题',
    '简介': '简介',
    '截止时间': '截止时间',
    '政策咨询机关': '简介',
    '政策咨询电话': '简介',
    '有效性': '简介',
    '生效时间': '简介',
    '废止时间': '简介',
}

# 关键字模糊匹配
def match_field(key):
    for k, v in FIELD_MAP.items():
        if k in key:
            return v
    return None

def extract_detail_fields_from_html(html, url, meta=None):
    """
    从详情页HTML中提取FIELDS字段，返回dict。
    meta: dict，包含来源站点、部门、级别等补充信息。
    """
    meta = meta or {}
    data = {k: '无' for k in FIELDS}
    data['详情地址'] = url
    for k in ['来源站点', '部门', '级别']:
        if k in meta:
            data[k] = meta[k]
    soup = BeautifulSoup(html, 'html.parser')
    # 1. 解析表格
    table = soup.find('table', class_='table_suoyin')
    if table:
        for row in table.find_all('tr'):
            cells = row.find_all(['th', 'td'])
            i = 0
            while i < len(cells) - 1:
                key = cells[i].get_text(strip=True).replace('\xa0', '').replace('\u3000', '').replace('&emsp;', '').replace(':', '').replace('：', '')
                val = cells[i+1].get_text(strip=True)
                field = match_field(key)
                if field:
                    # 多值合并
                    if data[field] == '无':
                        data[field] = val
                    else:
                        data[field] += ' ' + val
                i += 2
    # 2. 标题优先级：表格>meta>h1
    if data['标题'] == '无':
        if '标题' in meta:
            data['标题'] = meta['标题']
        else:
            h1 = soup.find(['h1'])
            if h1:
                data['标题'] = h1.get_text(strip=True)
    # 3. 正文内容
    content = ''
    article = soup.find('article')
    if article:
        content = article.get_text(separator='\n', strip=True)
    if not content:
        # 兜底所有p
        ps = soup.find_all('p')
        content = '\n'.join([p.get_text(strip=True) for p in ps if p.get_text(strip=True)])
    data['正文内容'] = content or '无'
    # 4. 敏感数字
    sens = re.findall(r"[\d.]+\s*(?:元|%|％|万元|亿元)", data['正文内容'])
    data['敏感数字'] = ', '.join(sens) if sens else '无'
    # 5. 附件
    attaches = []
    for a in soup.find_all('a', href=True):
        name = a.get_text(strip=True)
        href = a['href']
        if name.startswith('下载') and any(x in href for x in ['.pdf', '.doc', '.xls', '.zip', '.rar']):
            if href.startswith('http'):
                full_href = href
            else:
                full_href = url.rsplit('/', 1)[0] + '/' + href.lstrip('/')
            attaches.append(f'=HYPERLINK("{full_href}", "{name}")')
    data['附件'] = '\n'.join(attaches) if attaches else '无'
    # 6. 文件名及链接
    if data['标题'] != '无':
        data['文件名及链接'] = f'=HYPERLINK("{url}", "{data["标题"]}")'
    # 7. 清洗所有字段
    for k in FIELDS:
        if not data[k] or str(data[k]).strip() == '':
            data[k] = '无'
        else:
            data[k] = str(data[k]).strip()
    return data

script_name = os.path.splitext(os.path.basename(__file__))[0]
site_info = get_site_config(script_name)
LIST_URL = site_info['url']
SOURCE_SITE = site_info.get('dept', site_info.get('来源站点', ''))
DEPT = site_info.get('dept', '')
LEVEL = site_info.get('level', '')

CONCURRENCY = 5

logger = get_logger(script_name)

async def fetch_all_detail_urls(page):
    detail_urls = []
    page_num = 1
    while True:
        await page.wait_for_selector('ul.clearfix.xxgk_nav_list > li.clearfix')
        items = await page.query_selector_all('ul.clearfix.xxgk_nav_list > li.clearfix')
        logger.info(f"第{page_num}页发现{len(items)}条记录")
        stop_flag = False
        for item in items:
            a = await item.query_selector('a.title')
            href = await a.get_attribute('href') if a else None
            title = (await a.inner_text()).strip() if a else ''
            pub_elem = await item.query_selector('span.date')
            pub_date = (await pub_elem.inner_text()).strip() if pub_elem else ''
            logger.info(f"条目标题: {title} | 发布时间: {pub_date}")
            if pub_date and '2025' not in pub_date:
                logger.info('遇到非2025年数据，提前终止采集。')
                stop_flag = True
                break
            if href:
                detail_urls.append((href, title, pub_date))
        logger.info(f"采集到的详情链接样本: {detail_urls[:3]}")
        if stop_flag:
            break
        next_btn = await page.query_selector('a:has-text("下一页")')
        if next_btn and await next_btn.is_enabled():
            await next_btn.click()
            page_num += 1
            logger.info(f'翻到第{page_num}页...')
            await page.wait_for_timeout(1500)
        else:
            break
    logger.info(f"共采集到{len(detail_urls)}条详情页URL")
    return detail_urls

async def extract_detail(page, url_title):
    url, list_title, list_pub_date = url_title if isinstance(url_title, tuple) and len(url_title) == 3 else (url_title[0], url_title[1], '')
    try:
        logger.info(f"采集详情页: {url}")
        await page.goto(url, wait_until="domcontentloaded")
        await page.wait_for_timeout(1000)
        html = await page.content()
        meta = {
            '来源站点': SOURCE_SITE,
            '部门': DEPT,
            '级别': LEVEL,
            '标题': list_title,
            '发布时间': list_pub_date
        }
        data = extract_detail_fields_from_html(html, url, meta)
        # 修正：主题优先用列表title，无则用详情页title
        if list_title:
            data['主题'] = list_title
        elif data.get('标题'):
            data['主题'] = data['标题']
        logger.info(f"详情页采集样本: {data}")
        return [data.get(f, '') for f in FIELDS]
    except Exception as e:
        logger.error(f'采集失败: {url}, 错误: {e}')
        logger.info(f'采集失败详情页HTML片段: {(await page.content())[:500]}')
        logger.exception(e)
        return ['' for _ in FIELDS]

async def gather_with_concurrency(n, tasks):
    semaphore = asyncio.Semaphore(n)
    async def sem_task(task):
        async with semaphore:
            return await task
    return await asyncio.gather(*(sem_task(task) for task in tasks))

async def main():
    logger.info(f"启动采集任务: {script_name}, 入口: {LIST_URL}")
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.goto(LIST_URL)
        detail_urls = await fetch_all_detail_urls(page)
        # 并发抓取详情页
        tasks = []
        for url_title in detail_urls:
            detail_page = await browser.new_page()
            tasks.append(extract_detail(detail_page, url_title))
        results = await gather_with_concurrency(CONCURRENCY, tasks)
        logger.info(f"详情页采集完成，共{len(results)}条")
        out_path = save_result_xlsx(script_name, FIELDS, results)
        logger.info(f'采集完成，已输出到{out_path}')
        await browser.close()

if __name__ == '__main__':
    asyncio.run(main()) 