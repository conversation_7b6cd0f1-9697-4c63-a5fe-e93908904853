#!/usr/bin/env python3
"""
简单的窗口移动测试脚本
"""

print("开始执行窗口移动脚本...")

try:
    import pygetwindow as gw
    import pyautogui
    
    print("库导入成功")
    
    # 获取所有窗口
    all_windows = gw.getAllWindows()
    print(f"找到 {len(all_windows)} 个窗口")
    
    # 显示前5个窗口
    for i, win in enumerate(all_windows[:5]):
        print(f"窗口 {i+1}: '{win.title}' (大小: {win.width}x{win.height})")
    
    # 尝试获取当前活动窗口
    try:
        active_window = gw.getActiveWindow()
        if active_window:
            print(f"当前活动窗口: '{active_window.title}'")
            
            # 获取屏幕尺寸
            screen_width, screen_height = pyautogui.size()
            print(f"屏幕尺寸: {screen_width} x {screen_height}")
            
            # 计算右下角位置
            x = screen_width - active_window.width - 20
            y = screen_height - active_window.height - 80
            
            print(f"将移动到位置: ({x}, {y})")
            
            # 移动窗口
            active_window.moveTo(x, y)
            active_window.activate()
            
            print("窗口移动完成！")
        else:
            print("无法获取活动窗口")
    except Exception as e:
        print(f"操作活动窗口时出错: {e}")
        
except ImportError as e:
    print(f"导入库失败: {e}")
except Exception as e:
    print(f"执行出错: {e}")

print("脚本执行完毕")
