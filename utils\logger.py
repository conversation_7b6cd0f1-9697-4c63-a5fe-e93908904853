from loguru import logger
import os

def get_logger(spider_name: str, log_dir: str = "logs"):
    log_folder = os.path.join(log_dir, spider_name)
    os.makedirs(log_folder, exist_ok=True)
    log_path = os.path.join(log_folder, f"{spider_name}.log")
    logger.remove()
    # 输出到文件
    logger.add(
        log_path,
        rotation="1 day",
        retention=5,
        encoding="utf-8",
        enqueue=True,
        backtrace=True,
        diagnose=True,
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    # 同步输出到控制台
    logger.add(
        sink=lambda msg: print(msg, end=""),
        level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    return logger 