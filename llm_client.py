import openai
import os

class LLMClient:
    def __init__(self, api_key=None, model="gpt-3.5-turbo"):
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.model = model
        openai.api_key = self.api_key

    def summarize(self, text, prompt="请对以下内容做简要总结："):
        messages = [
            {"role": "system", "content": "你是一个专业的中文内容摘要助手。"},
            {"role": "user", "content": f"{prompt}\n{text}"}
        ]
        response = openai.ChatCompletion.create(
            model=self.model,
            messages=messages,
            temperature=0.3,
            max_tokens=512
        )
        return response.choices[0].message["content"].strip() 