# PTL 爬虫平台

## 项目简介
本项目是一个基于 FastAPI 的多站点自动化爬虫平台，支持灵活配置、异步与多线程采集、结果导出 Excel、详细日志记录，并可选用 OpenAI 进行内容摘要。

## 主要功能
- 支持多站点配置，按需调度爬虫脚本
- 异步与多线程采集，提升抓取效率
- 采集结果自动导出为 Excel 文件，支持超链接
- 详细日志记录，便于排查与追踪
- 可选 OpenAI LLM 内容摘要能力
- API 接口统一调度爬虫

## 目录结构
```
├── main.py                # FastAPI 主入口，API 服务
├── requirements.txt       # 依赖库
├── llm_client.py          # OpenAI LLM 客户端（可选）
├── config/
│   └── sites.yaml         # 站点配置文件
├── spiders/
│   └── spider_xxx.py      # 各站点爬虫脚本
├── utils/
│   ├── output.py          # 结果导出工具
│   ├── logger.py          # 日志工具
│   ├── fields.py          # 字段与配置处理
│   └── thread_pool.py     # 多线程工具
└── result/                # 采集结果输出目录
```

## 依赖安装
```bash
pip install -r requirements.txt
```
如需使用 Playwright，请先安装浏览器驱动：
```bash
playwright install
```

## 配置说明
- 站点配置位于 `config/sites.yaml`，每个站点一条，需指定 `script` 字段与爬虫脚本对应。
- 字段说明：
  - `dept`: 部门
  - `province`: 省份
  - `url`: 列表页地址
  - `script`: 爬虫脚本名（如 spider_063）

## 运行方式
### 启动 API 服务
```bash
python main.py --host 0.0.0.0 --port 9854
```

### 通过 API 调度爬虫
POST `/run_spider`
```json
{
  "dept": "财政厅",      // 可选
  "province": "安徽省",  // 可选
  "script": "spider_063" // 可选
}
```
三者至少填一个，匹配到的站点会自动调度对应爬虫。

## 结果导出
- 采集结果自动导出至 `result/<script>/` 目录下的 Excel 文件。
- 附件与文件名字段支持超链接。

## 日志说明
- 日志按爬虫脚本分文件存储于 `logs/<script>/` 目录，便于排查。

## 扩展爬虫脚本
1. 在 `spiders/` 目录下新增 `spider_xxx.py`，实现 `async def main()` 入口。
2. 在 `config/sites.yaml` 添加对应配置，`script` 字段与脚本名一致。
3. 脚本可调用 `utils` 工具进行字段处理、日志、结果导出等。

## OpenAI 摘要（可选）
- 配置 OpenAI API Key 后，可用 `llm_client.py` 进行内容摘要。
- 用法示例：
```python
from llm_client import LLMClient
llm = LLMClient(api_key="sk-xxx")
sum = llm.summarize("待摘要内容")
```

## 联系与反馈
如有问题或建议，欢迎提交 issue 或联系开发者。 