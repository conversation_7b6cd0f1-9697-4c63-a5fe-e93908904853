import os
import yaml

def get_site_config(script_name: str, yaml_path: str = None):
    """
    根据脚本名从config/sites.yaml读取并返回对应站点配置。
    :param script_name: 脚本名（不含.py）
    :param yaml_path: yaml路径，默认../config/sites.yaml
    :return: 站点配置dict
    """
    if yaml_path is None:
        yaml_path = os.path.join(os.path.dirname(__file__), '../config/sites.yaml')
    with open(yaml_path, encoding='utf-8') as f:
        sites_cfg = yaml.safe_load(f)
    site_info = next((s for s in sites_cfg['sites'] if s.get('script') == script_name), None)
    if not site_info:
        raise ValueError(f'未在{yaml_path}中找到脚本{script_name}的配置')
    return site_info 