from fastapi import FastAPI, Body
from pydantic import BaseModel
from typing import List, Optional
import importlib
import os
from utils.fields import get_site_config
import yaml
import argparse
import uvicorn

app = FastAPI()

# 读取所有站点配置
def load_all_sites(yaml_path=None):
    if yaml_path is None:
        yaml_path = os.path.join(os.path.dirname(__file__), 'config/sites.yaml')
    with open(yaml_path, encoding='utf-8') as f:
        return yaml.safe_load(f)['sites']

class SpiderRequest(BaseModel):
    dept: Optional[str] = None
    province: Optional[str] = None
    script: Optional[str] = None

@app.post('/run_spider')
async def run_spider(req: SpiderRequest = Body(...)):
    # 参数校验
    if not (req.dept or req.province or req.script):
        return {"error": "参数dept、province、script至少有一个不为空"}
    # 匹配站点
    sites = load_all_sites()
    matched = []
    for s in sites:
        if req.script and s.get('script') == req.script:
            matched.append(s)
        elif req.dept and s.get('dept') == req.dept:
            matched.append(s)
        elif req.province and s.get('province') == req.province:
            matched.append(s)
    if not matched:
        return {"error": "未找到匹配的站点配置"}
    results = []
    for site in matched:
        script_name = site['script']
        try:
            spider_mod = importlib.import_module(f'spiders.{script_name}')
            # 约定每个spider有async def main()
            if hasattr(spider_mod, 'main'):
                import asyncio
                asyncio.create_task(spider_mod.main())
                results.append({"script": script_name, "status": "started"})
            else:
                results.append({"script": script_name, "status": "no main()"})
        except Exception as e:
            results.append({"script": script_name, "status": f"error: {e}"})
    return {"matched": [s['script'] for s in matched], "results": results} 

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="PTL爬虫API服务启动参数")
    parser.add_argument('--host', type=str, default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=9854, help='监听端口')
    parser.add_argument('--reload', action='store_true', help='开发模式自动重载')
    args = parser.parse_args()
    uvicorn.run("main:app", host=args.host, port=args.port, reload=args.reload)