#!/usr/bin/env python3
"""
spider_114.py - 常州市工业和信息化局政策文件爬虫
网站: https://gxj.changzhou.gov.cn/class/HLKBOAJN
使用 Playwright 进行数据采集
"""

import asyncio
import sys
import os
import re
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import requests
from playwright.async_api import async_playwright
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.fields import get_site_config
from utils.logger import get_logger
from utils.output import save_result_xlsx

# 配置
SITE_ID = 'spider_114'
BASE_URL = 'https://gxj.changzhou.gov.cn/class/HLKBOAJN'
CURRENT_YEAR = str(datetime.now().year)

# 定义字段
FIELDS = [
    '来源站点', '部门', '级别', '标题', '简介', '发布时间', '截止时间', '发文机关', '主题', '索引号', '文号',
    '详情地址', '正文内容', '敏感数字', '附件', '文件名及链接'
]

def extract_detail_info(url, meta=None):
    """提取详情页信息"""
    logger = get_logger(SITE_ID)
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 初始化数据
        data = {
            '来源站点': '常州市工业和信息化局',
            '部门': '工信局',
            '级别': '市级',
            '标题': meta.get('标题', '无') if meta else '无',
            '简介': '无',
            '发布时间': '无',
            '截止时间': '无',
            '发文机关': '无',
            '主题': '无',
            '索引号': '无',
            '文号': '无',
            '详情地址': url,
            '正文内容': '无',
            '敏感数字': '无',
            '附件': '无',
            '文件名及链接': '无'
        }
        
        # 1. 使用meta中的标题
        if meta and meta.get('标题'):
            data['标题'] = meta['标题']
        
        # 2. 提取发布时间
        if meta and meta.get('发布时间'):
            data['发布时间'] = meta['发布时间']
        
        # 3. 提取来源机构 - 只做最小修改清理nbsp
        source_pattern = r'来源：([^浏览]+)'
        source_match = re.search(source_pattern, response.text)
        if source_match:
            source = source_match.group(1).strip()
            # 清理各种异常字符
            source = re.sub(r'&nbsp;?', '', source)  # 清理&nbsp;
            source = re.sub(r'nbsp+', '', source)    # 清理nbsp
            source = re.sub(r'&[a-zA-Z]+;', '', source)  # 清理其他HTML实体
            source = re.sub(r'两化融合推进处', '', source)  # 清理特定文本
            source = source.strip()

            # 统一发文机关名称
            if '工信局' in source or '工业和信息化局' in source or not source:
                data['发文机关'] = '常州市工业和信息化局'
            elif '政府办' in source or '办公室' in source:
                data['发文机关'] = '常州市人民政府办公室'
            else:
                data['发文机关'] = source if source else '常州市工业和信息化局'
        else:
            data['发文机关'] = '常州市工业和信息化局'
        
        # 4. 提取正文内容
        content_divs = soup.find_all(['div', 'td', 'p'])
        content_lines = []
        
        for div in content_divs:
            text = div.get_text(strip=True)
            if (len(text) > 50 and 
                '您的位置' not in text and 
                '发布日期' not in text and 
                '来源：' not in text and
                '浏览次数' not in text):
                content_lines.append(text)
        
        if content_lines:
            unique_content = []
            for line in content_lines:
                if line not in unique_content:
                    unique_content.append(line)
            data['正文内容'] = '\n'.join(unique_content)
        
        # 5. 提取敏感数字
        if data['正文内容'] != '无':
            sensitive_patterns = [
                r'\d+(?:\.\d+)?(?:万|亿)?元',
                r'\d+(?:\.\d+)?%',
                r'\d{4}年',
                r'\d+家',
                r'\d+个',
                r'\d+项'
            ]
            
            sensitive_numbers = []
            for pattern in sensitive_patterns:
                matches = re.findall(pattern, data['正文内容'])
                sensitive_numbers.extend(matches)
            
            if sensitive_numbers:
                unique_numbers = list(set(sensitive_numbers))[:10]
                data['敏感数字'] = ', '.join(unique_numbers)
        
        # 6. 生成文件名及链接
        data['文件名及链接'] = f'=HYPERLINK("{url}", "{data["标题"]}")'
        
        # 7. 提取主题
        title = data['标题']
        if '通知' in title:
            data['主题'] = '通知'
        elif '公告' in title or '公示' in title:
            data['主题'] = '公告公示'
        elif '方案' in title or '计划' in title:
            data['主题'] = '实施方案'
        elif '办法' in title or '细则' in title:
            data['主题'] = '管理办法'
        else:
            data['主题'] = '政策文件'
        
        # 8. 简介
        if data['正文内容'] != '无':
            content_sentences = data['正文内容'].split('。')
            if len(content_sentences) > 0:
                intro_sentences = content_sentences[:2]
                intro = '。'.join(intro_sentences)
                if len(intro) > 200:
                    intro = intro[:200] + '...'
                data['简介'] = intro
        
        logger.info(f"成功提取详情页信息: {data['标题']}")
        return data
        
    except Exception as e:
        logger.error(f"提取详情页信息失败 {url}: {e}")
        return None

async def crawl_list_pages():
    """爬取列表页数据"""
    logger = get_logger(SITE_ID)
    
    all_detail_urls = []
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # 访问首页
        await page.goto(BASE_URL)
        await page.wait_for_timeout(3000)
        
        page_num = 1
        
        while True:
            logger.info(f'正在采集第{page_num}页...')
            
            # 等待页面加载
            await page.wait_for_timeout(2000)
            
            # 提取当前页面的链接和日期
            js_code = f'''() => {{
                const data = [];
                const currentYear = "{CURRENT_YEAR}";

                // 查找表格中的数据行
                const rows = document.querySelectorAll('table tr');

                for (let i = 0; i < rows.length; i++) {{
                    const row = rows[i];
                    const cells = row.querySelectorAll('td');

                    if (cells.length >= 2) {{
                        const titleCell = cells[0];
                        const dateCell = cells[1];

                        const link = titleCell.querySelector('a');
                        if (link && link.href && link.href.includes('/html/')) {{
                            const title = link.textContent.trim();
                            const url = link.href;

                            // 提取日期
                            const dateText = dateCell.textContent.trim();
                            const dateMatch = dateText.match(/\\[(\\d{{4}}-\\d{{2}}-\\d{{2}})\\]/);
                            const date = dateMatch ? dateMatch[1] : '';

                            // 检查年份
                            const isCurrentYear = date.includes(currentYear);

                            data.push({{
                                title: title,
                                url: url,
                                date: date,
                                isCurrentYear: isCurrentYear
                            }});
                        }}
                    }}
                }}

                return data;
            }}'''
            detail_urls = await page.evaluate(js_code)
            
            logger.info(f"第{page_num}页发现{len(detail_urls)}条记录")
            
            # 检查是否有非当前年份的数据
            stop_flag = False
            current_year_count = 0
            
            for item in detail_urls:
                # 只有当日期不为空且确实不是当前年份时才停止
                if item['date'] and not item['isCurrentYear']:
                    logger.info(f'遇到非{CURRENT_YEAR}年数据: {item["date"]}，提前终止采集。')
                    stop_flag = True
                    break
                
                # 如果是当前年份的数据，则添加
                if item['isCurrentYear']:
                    # 检查是否已存在相同URL，避免重复
                    url_exists = any(existing_url == item['url'] for existing_url, _, _ in all_detail_urls)
                    if not url_exists:
                        all_detail_urls.append((item['url'], item['title'], item['date']))
                        current_year_count += 1
            
            logger.info(f"第{page_num}页采集到{current_year_count}条{CURRENT_YEAR}年数据")
            
            if stop_flag:
                break
            
            # 查找下一页按钮
            next_page_num = page_num + 1
            next_url = f'{BASE_URL}/{next_page_num}'
            
            # 检查是否还有下一页
            if page_num >= 12:  # 总共12页
                logger.info('已到达最后一页')
                break
            
            # 导航到下一页
            await page.goto(next_url)
            page_num += 1
            logger.info(f'翻到第{page_num}页...')
            await page.wait_for_timeout(2000)
        
        await browser.close()
    
    logger.info(f"总共采集到{len(all_detail_urls)}条详情页URL")
    return all_detail_urls

async def main():
    """主函数"""
    logger = get_logger(SITE_ID)
    
    logger.info(f"开始爬取 {SITE_ID}")
    
    try:
        # 1. 爬取列表页，获取详情页URL
        detail_urls = await crawl_list_pages()
        
        if not detail_urls:
            logger.warning("未获取到任何详情页URL")
            return
        
        # 2. 爬取详情页
        results = []
        for i, (url, title, date) in enumerate(detail_urls, 1):
            logger.info(f"正在处理第{i}/{len(detail_urls)}个详情页: {title}")
            
            meta = {
                '标题': title,
                '发布时间': date
            }
            
            detail_info = extract_detail_info(url, meta)
            if detail_info:
                # 转换为列表格式
                row = [detail_info.get(field, '无') for field in FIELDS]
                results.append(row)
            
            # 添加延时避免请求过快
            await asyncio.sleep(1)
        
        # 3. 保存结果
        if results:
            save_result_xlsx(SITE_ID, FIELDS, results)
            logger.info(f"爬取完成，共获取{len(results)}条数据")
        else:
            logger.warning("未获取到任何有效数据")
            
    except Exception as e:
        logger.error(f"爬取过程中出现错误: {e}")
        raise

if __name__ == '__main__':
    asyncio.run(main())
