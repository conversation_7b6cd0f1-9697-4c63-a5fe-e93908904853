from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor, as_completed

class SpiderThreadPool:
    def __init__(self, max_workers=8):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    def run_tasks(self, func, tasks):
        futures = [self.executor.submit(func, *task) for task in tasks]
        results = []
        for future in as_completed(futures):
            try:
                results.append(future.result())
            except Exception as e:
                results.append(e)
        return results 