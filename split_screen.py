#!/usr/bin/env python3
"""
分屏窗口脚本 - 将当前活动窗口调整为右半屏
模拟图片中的分屏效果
"""

import pygetwindow as gw
import pyautogui

print("=== 分屏窗口脚本开始 ===")

try:
    # 获取屏幕尺寸
    screen_width, screen_height = pyautogui.size()
    print(f"屏幕尺寸: {screen_width} x {screen_height}")
    
    # 查找指定的 Anaconda Prompt 窗口
    target_window = None

    # 尝试查找 Anaconda Prompt 窗口
    anaconda_windows = gw.getWindowsWithTitle('Anaconda Prompt')
    if anaconda_windows:
        target_window = anaconda_windows[0]
        print(f"找到 Anaconda Prompt 窗口: '{target_window.title}'")
    else:
        print("未找到 Anaconda Prompt 窗口，尝试查找其他相似窗口...")
        # 尝试查找其他可能的窗口名称
        possible_titles = ['anaconda prompt', 'Anaconda Prompt', 'Command Prompt', 'PowerShell', 'Windows PowerShell', 'cmd']
        for title in possible_titles:
            windows = gw.getWindowsWithTitle(title)
            if windows:
                target_window = windows[0]
                print(f"找到窗口: '{title}'")
                break

    if target_window:
        print(f"目标窗口: '{target_window.title}'")
        print(f"原始窗口尺寸: {target_window.width} x {target_window.height}")

        # 计算右半屏的尺寸和位置
        # 宽度为屏幕宽度的50%
        new_width = int(screen_width * 0.5)
        # 高度为屏幕高度减去任务栏高度
        new_height = int(screen_height - 40)  # 减去任务栏高度

        # 位置：右半屏
        x = screen_width - new_width  # 紧贴右边
        y = 0  # 从顶部开始

        print(f"目标尺寸: {new_width} x {new_height}")
        print(f"目标位置: ({x}, {y})")

        # 先移动窗口位置
        target_window.moveTo(x, y)
        print("窗口位置已移动")

        # 再调整窗口大小
        target_window.resizeTo(new_width, new_height)
        print("窗口大小已调整")

        # 激活窗口确保置顶
        target_window.activate()

        print(f"Anaconda Prompt 窗口已成功调整为右半屏分屏模式")

    else:
        print("未找到 Anaconda Prompt 或其他可操作的窗口")

except Exception as e:
    print(f"执行出错: {e}")

print("=== 分屏窗口脚本结束 ===")
