#!/usr/bin/env python3
"""
专门针对 Anaconda Prompt 的分屏脚本
"""

import pygetwindow as gw
import pyautogui

print("=== Anaconda Prompt 分屏脚本开始 ===")

try:
    # 获取屏幕尺寸
    screen_width, screen_height = pyautogui.size()
    print(f"屏幕尺寸: {screen_width} x {screen_height}")
    
    # 查找所有窗口，显示前10个
    all_windows = gw.getAllWindows()
    print(f"总共找到 {len(all_windows)} 个窗口")
    print("前10个窗口:")
    for i, win in enumerate(all_windows[:10]):
        print(f"  {i+1}. '{win.title}' (大小: {win.width}x{win.height})")
    
    # 查找 Anaconda Prompt 窗口
    target_window = None
    
    # 尝试多种可能的窗口标题
    possible_titles = ['Anaconda Prompt', 'anaconda prompt', 'Anaconda Prompt - python', 'Command Prompt']
    
    for title in possible_titles:
        windows = gw.getWindowsWithTitle(title)
        if windows:
            target_window = windows[0]
            print(f"找到目标窗口: '{target_window.title}'")
            break
    
    if target_window:
        print(f"原始窗口尺寸: {target_window.width} x {target_window.height}")
        print(f"原始窗口位置: ({target_window.left}, {target_window.top})")
        
        # 计算右半屏的尺寸和位置
        new_width = int(screen_width * 0.5)  # 屏幕宽度的50%
        new_height = int(screen_height - 40)  # 屏幕高度减去任务栏
        x = screen_width - new_width  # 右半屏位置
        y = 0  # 从顶部开始
        
        print(f"目标尺寸: {new_width} x {new_height}")
        print(f"目标位置: ({x}, {y})")
        
        # 移动和调整窗口
        target_window.moveTo(x, y)
        print("窗口位置已移动")
        
        target_window.resizeTo(new_width, new_height)
        print("窗口大小已调整")
        
        # 激活窗口
        target_window.activate()
        print("窗口已激活")
        
        print(f"Anaconda Prompt 窗口已成功调整为右半屏分屏模式")
        
    else:
        print("未找到 Anaconda Prompt 窗口")
        print("请确保 Anaconda Prompt 窗口已打开")

except Exception as e:
    print(f"执行出错: {e}")
    import traceback
    traceback.print_exc()

print("=== 脚本执行完毕 ===")
