#!/usr/bin/env python3
"""
spider_039.py - 常州市财政局政府信息公开爬虫
网站: https://www.changzhou.gov.cn/gi_class/czj#czj_02_04
使用 Playwright 进行数据采集
"""

import asyncio
import sys
import os
import re
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import requests
from playwright.async_api import async_playwright
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.fields import get_site_config
from utils.logger import get_logger
from utils.output import save_result_xlsx

# 配置
SITE_ID = 'spider_039'
BASE_URL = 'https://www.changzhou.gov.cn/gi_class/czj#czj_02_04'
CURRENT_YEAR = str(datetime.now().year)

def extract_detail_info(url, meta=None):
    """提取详情页信息"""
    logger = get_logger(SITE_ID)
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 初始化数据
        data = {
            '来源站点': '常州市财政局',
            '部门': '财政局',
            '级别': '市级',
            '标题': meta.get('标题', '无') if meta else '无',
            '简介': '无',
            '发布时间': '无',
            '截止时间': '无',
            '发文机关': '无',
            '主题': '无',
            '索引号': '无',
            '文号': '无',
            '详情地址': url,
            '正文内容': '无',
            '敏感数字': '无',
            '附件': '无',
            '文件名及链接': '无'
        }
        
        # 1. 提取标题
        title_elem = soup.find('td', string=re.compile(r'信息名称：'))
        if title_elem:
            title_text = title_elem.get_text(strip=True)
            title_match = re.search(r'信息名称：(.*)', title_text)
            if title_match:
                data['标题'] = title_match.group(1).strip()
        
        # 2. 提取索引号
        index_elem = soup.find('td', string=re.compile(r'索 引 号：'))
        if index_elem:
            index_text = index_elem.get_text(strip=True)
            index_match = re.search(r'索 引 号：(.*)', index_text)
            if index_match:
                data['索引号'] = index_match.group(1).strip()
        
        # 3. 提取文件编号
        file_num_elem = soup.find('td', string=re.compile(r'文件编号：'))
        if file_num_elem:
            file_num_text = file_num_elem.get_text(strip=True)
            file_num_match = re.search(r'文件编号：(.*)', file_num_text)
            if file_num_match:
                data['文号'] = file_num_match.group(1).strip()
        
        # 4. 提取发布机构
        org_elem = soup.find('td', string=re.compile(r'发布机构：'))
        if org_elem:
            org_text = org_elem.get_text(strip=True)
            org_match = re.search(r'发布机构：(.*)', org_text)
            if org_match:
                data['发文机关'] = org_match.group(1).strip()
        
        # 5. 提取生成日期和公开日期
        gen_date_elem = soup.find('td', string=re.compile(r'生成日期：'))
        if gen_date_elem:
            gen_date_text = gen_date_elem.get_text(strip=True)
            gen_date_match = re.search(r'生成日期：(\d{4}-\d{2}-\d{2})', gen_date_text)
            if gen_date_match:
                data['发布时间'] = gen_date_match.group(1)
        
        pub_date_elem = soup.find('td', string=re.compile(r'公开日期：'))
        if pub_date_elem and data['发布时间'] == '无':
            pub_date_text = pub_date_elem.get_text(strip=True)
            pub_date_match = re.search(r'公开日期：(\d{4}-\d{2}-\d{2})', pub_date_text)
            if pub_date_match:
                data['发布时间'] = pub_date_match.group(1)
        
        # 如果详情页没有日期，使用列表页传入的日期
        if data['发布时间'] == '无' and meta and meta.get('发布时间'):
            data['发布时间'] = meta['发布时间']
        
        # 6. 提取内容概述
        summary_elem = soup.find('td', string=re.compile(r'内容概述：'))
        if summary_elem:
            summary_text = summary_elem.get_text(strip=True)
            summary_match = re.search(r'内容概述：(.*)', summary_text)
            if summary_match:
                data['简介'] = summary_match.group(1).strip()
        
        # 7. 提取正文内容
        content_tables = soup.find_all('table')
        for table in content_tables:
            # 查找包含正文内容的表格
            paragraphs = table.find_all('p')
            if paragraphs and len(paragraphs) > 3:  # 正文通常有多个段落
                content_lines = []
                for p in paragraphs:
                    text = p.get_text(strip=True)
                    if text and text not in ['扫一扫在手机打开当前页']:
                        content_lines.append(text)
                
                if content_lines:
                    data['正文内容'] = '\n'.join(content_lines)
                    break
        
        # 8. 提取敏感数字
        if data['正文内容'] != '无':
            # 查找金额、百分比等敏感数字
            sensitive_patterns = [
                r'\d+(?:\.\d+)?(?:万|亿)?元',
                r'\d+(?:\.\d+)?%',
                r'\d+(?:\.\d+)?％',
                r'\d+(?:\.\d+)?(?:万|亿)',
            ]
            
            sensitive_numbers = []
            for pattern in sensitive_patterns:
                matches = re.findall(pattern, data['正文内容'])
                sensitive_numbers.extend(matches)
            
            if sensitive_numbers:
                data['敏感数字'] = ', '.join(list(set(sensitive_numbers)))
        
        # 9. 提取附件
        download_link = soup.find('a', string=re.compile(r'下载本信息公开文件'))
        if download_link and download_link.get('href'):
            href = download_link['href']
            if href.startswith('/'):
                full_href = 'https://www.changzhou.gov.cn' + href
            else:
                full_href = href
            data['附件'] = f'=HYPERLINK("{full_href}", "下载本信息公开文件")'
        
        # 10. 生成文件名及链接
        data['文件名及链接'] = f'=HYPERLINK("{url}", "{data["标题"]}")'
        
        # 11. 提取主题（从标题中推断）
        title = data['标题']
        if '通知' in title:
            data['主题'] = '通知'
        elif '公告' in title or '公示' in title:
            data['主题'] = '公告公示'
        elif '方案' in title:
            data['主题'] = '实施方案'
        elif '办法' in title or '细则' in title:
            data['主题'] = '管理办法'
        else:
            data['主题'] = '政策文件'
        
        logger.info(f"成功提取详情页信息: {data['标题']}")
        return data
        
    except Exception as e:
        logger.error(f"提取详情页信息失败 {url}: {e}")
        return None

async def crawl_list_pages():
    """爬取列表页数据"""
    logger = get_logger(SITE_ID)
    
    all_detail_urls = []
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # 访问首页
        await page.goto(BASE_URL)
        await page.wait_for_timeout(3000)  # 等待iframe加载
        
        page_num = 1
        
        while True:
            logger.info(f'正在采集第{page_num}页...')
            
            # 等待页面加载
            await page.wait_for_timeout(2000)
            
            # 提取当前页面的链接和日期
            js_code = f'''() => {{
                const data = [];
                const currentYear = "{CURRENT_YEAR}";

                // 查找iframe中的表格数据
                const iframe = document.querySelector('iframe');
                if (iframe && iframe.contentDocument) {{
                    const iframeDoc = iframe.contentDocument;
                    const rows = iframeDoc.querySelectorAll('table tr');

                    for (let i = 1; i < rows.length; i++) {{ // 跳过表头
                        const row = rows[i];
                        const cells = row.querySelectorAll('td');

                        if (cells.length >= 3) {{
                            const indexNumber = cells[0].textContent.trim();
                            const titleCell = cells[1];
                            const dateCell = cells[2];

                            const link = titleCell.querySelector('a');
                            if (link) {{
                                const title = link.textContent.trim();
                                const url = link.href;
                                const date = dateCell.textContent.trim();

                                // 检查年份
                                const isCurrentYear = date.includes(currentYear);

                                data.push({{
                                    indexNumber: indexNumber,
                                    title: title,
                                    url: url,
                                    date: date,
                                    isCurrentYear: isCurrentYear
                                }});
                            }}
                        }}
                    }}
                }}

                return data;
            }}'''
            detail_urls = await page.evaluate(js_code)
            
            logger.info(f"第{page_num}页发现{len(detail_urls)}条记录")
            
            # 检查是否有非当前年份的数据
            stop_flag = False
            for item in detail_urls:
                if not item['isCurrentYear']:
                    logger.info(f'遇到非{CURRENT_YEAR}年数据: {item["date"]}，提前终止采集。')
                    stop_flag = True
                    break
                
                # 添加到总列表
                all_detail_urls.append((item['url'], item['title'], item['date'], item['indexNumber']))
            
            if stop_flag:
                break
            
            # 查找下一页按钮
            next_page_num = page_num + 1
            next_url = f'https://www.changzhou.gov.cn/gi_class/czj_02_04/{next_page_num}'
            
            # 检查是否还有下一页
            if page_num >= 49:  # 总共49页
                logger.info('已到达最后一页')
                break
            
            # 导航到下一页
            await page.goto(next_url)
            page_num += 1
            logger.info(f'翻到第{page_num}页...')
            await page.wait_for_timeout(2000)
        
        await browser.close()
    
    logger.info(f"总共采集到{len(all_detail_urls)}条详情页URL")
    return all_detail_urls

# 定义字段
FIELDS = [
    '来源站点', '部门', '级别', '标题', '简介', '发布时间', '截止时间', '发文机关', '主题', '索引号', '文号',
    '详情地址', '正文内容', '敏感数字', '附件', '文件名及链接'
]

async def main():
    """主函数"""
    logger = get_logger(SITE_ID)
    config = get_site_config(SITE_ID)

    logger.info(f"开始爬取 {SITE_ID}")

    try:
        # 1. 爬取列表页，获取详情页URL
        detail_urls = await crawl_list_pages()

        if not detail_urls:
            logger.warning("未获取到任何详情页URL")
            return

        # 2. 爬取详情页
        results = []
        for i, (url, title, date, index_number) in enumerate(detail_urls, 1):
            logger.info(f"正在处理第{i}/{len(detail_urls)}个详情页: {title}")

            meta = {
                '标题': title,
                '发布时间': date,
                '索引号': index_number
            }

            detail_info = extract_detail_info(url, meta)
            if detail_info:
                # 转换为列表格式
                row = [detail_info.get(field, '无') for field in FIELDS]
                results.append(row)

            # 添加延时避免请求过快
            await asyncio.sleep(1)

        # 3. 保存结果
        if results:
            save_result_xlsx(SITE_ID, FIELDS, results)
            logger.info(f"爬取完成，共获取{len(results)}条数据")
        else:
            logger.warning("未获取到任何有效数据")

    except Exception as e:
        logger.error(f"爬取过程中出现错误: {e}")
        raise

if __name__ == '__main__':
    asyncio.run(main())
