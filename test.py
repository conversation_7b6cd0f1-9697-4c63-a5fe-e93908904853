#!/usr/bin/env python3
"""
窗口移动到右下角并置顶脚本
"""

import pygetwindow as gw
import pyautogui

print("=== 窗口移动脚本开始 ===")

try:
    # 获取所有窗口
    all_windows = gw.getAllWindows()
    print(f"总共找到 {len(all_windows)} 个窗口")

    # 显示前10个窗口
    print("当前窗口列表:")
    for i, win in enumerate(all_windows[:10]):
        print(f"  {i+1}. '{win.title}' (大小: {win.width}x{win.height})")

    # 尝试获取当前活动窗口
    window = None
    window_title = None

    try:
        window = gw.getActiveWindow()
        if window:
            window_title = window.title
            print(f"使用当前活动窗口: '{window_title}'")
        else:
            print("无法获取活动窗口")
    except Exception as e:
        print(f"获取活动窗口时出错: {e}")

    # 如果没有活动窗口，尝试查找常见的终端窗口
    if not window:
        possible_titles = ['Anaconda Prompt', 'Command Prompt', 'PowerShell', 'Windows PowerShell', 'cmd']
        for title in possible_titles:
            windows = gw.getWindowsWithTitle(title)
            if windows:
                window = windows[0]
                window_title = title
                print(f"找到窗口: '{window_title}'")
                break

    if window:
        try:
            # 获取屏幕尺寸
            screen_width, screen_height = pyautogui.size()
            print(f"屏幕尺寸: {screen_width} x {screen_height}")

            # 获取窗口尺寸
            window_width = window.width
            window_height = window.height
            print(f"原始窗口尺寸: {window_width} x {window_height}")

            # 根据图片效果，将窗口调整为屏幕右半部分（类似分屏效果）
            # 新宽度约为屏幕宽度的50%
            new_width = int(screen_width * 0.5)
            # 新高度约为屏幕高度的100%（减去任务栏高度）
            new_height = int(screen_height - 80)  # 减去任务栏和标题栏高度

            # 调整窗口大小
            window.resizeTo(new_width, new_height)
            print(f"调整后窗口尺寸: {new_width} x {new_height}")

            # 将窗口移动到屏幕右半部分（类似图片中的分屏效果）
            x = screen_width - new_width  # 紧贴右边
            y = 0  # 从顶部开始

            # 移动窗口到右半屏
            window.moveTo(x, y)

            # 将窗口置顶
            window.activate()  # 激活窗口

            print(f'窗口 "{window_title}" 已调整为右半屏分屏模式，位置 ({x}, {y})')
        except Exception as e:
            print(f"调整窗口时出错: {e}")
    else:
        print("未找到可操作的窗口")

except Exception as e:
    print(f"脚本执行出错: {e}", flush=True)

print("=== 窗口移动脚本结束 ===", flush=True)






