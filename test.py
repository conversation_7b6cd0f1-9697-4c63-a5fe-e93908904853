#!/usr/bin/env python3
"""
窗口移动到右下角并置顶脚本
"""

import pygetwindow as gw
import pyautogui

print("=== 窗口移动脚本开始 ===")

try:
    # 获取所有窗口
    all_windows = gw.getAllWindows()
    print(f"总共找到 {len(all_windows)} 个窗口")

    # 显示前10个窗口
    print("当前窗口列表:")
    for i, win in enumerate(all_windows[:10]):
        print(f"  {i+1}. '{win.title}' (大小: {win.width}x{win.height})")

    # 尝试获取当前活动窗口
    window = None
    window_title = None

    try:
        window = gw.getActiveWindow()
        if window:
            window_title = window.title
            print(f"使用当前活动窗口: '{window_title}'")
        else:
            print("无法获取活动窗口")
    except Exception as e:
        print(f"获取活动窗口时出错: {e}")

    # 如果没有活动窗口，尝试查找常见的终端窗口
    if not window:
        possible_titles = ['Anaconda Prompt', 'Command Prompt', 'PowerShell', 'Windows PowerShell', 'cmd']
        for title in possible_titles:
            windows = gw.getWindowsWithTitle(title)
            if windows:
                window = windows[0]
                window_title = title
                print(f"找到窗口: '{window_title}'")
                break

    if window:
        try:
            # 获取屏幕尺寸
            screen_width, screen_height = pyautogui.size()
            print(f"屏幕尺寸: {screen_width} x {screen_height}")

            # 获取窗口尺寸
            window_width = window.width
            window_height = window.height
            print(f"窗口尺寸: {window_width} x {window_height}")

            # 计算右下角位置（留一些边距）
            x = screen_width - window_width - 10  # 右边留10像素边距
            y = screen_height - window_height - 50  # 底部留50像素边距（考虑任务栏）

            # 移动窗口到右下角
            window.moveTo(x, y)

            # 将窗口置顶
            window.activate()  # 激活窗口

            print(f'窗口 "{window_title}" 已移动到右下角位置 ({x}, {y}) 并置顶')
        except Exception as e:
            print(f"移动窗口时出错: {e}")
    else:
        print("未找到可操作的窗口")

except Exception as e:
    print(f"脚本执行出错: {e}", flush=True)

print("=== 窗口移动脚本结束 ===", flush=True)






