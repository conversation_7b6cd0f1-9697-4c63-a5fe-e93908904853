import asyncio
from playwright.async_api import async_playwright
import re
import os
import sys
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.fields import get_site_config
from utils.logger import get_logger
from utils.output import save_result_xlsx
from bs4 import BeautifulSoup

FIELDS = [
    '来源站点', '部门', '级别', '标题', '简介', '发布时间', '截止时间', '发文机关', '主题', '索引号', '文号',
    '详情地址', '正文内容', '敏感数字', '附件', '文件名及链接'
]

# 字段映射规则（表头关键字到FIELDS）
FIELD_MAP = {
    '索引号': '索引号',
    '信息分类': '主题',
    '主题分类': '主题',
    '发文机关': '发文机关',
    '发布机构': '发文机关',
    '成文日期': '发布时间',
    '发布日期': '发布时间',
    '生成日期': '发布时间',
    '发文字号': '文号',
    '文号': '文号',
    '标题': '标题',
    '名称': '标题',
    '简介': '简介',
    '截止时间': '截止时间',
    '政策咨询机关': '简介',
    '政策咨询电话': '简介',
    '有效性': '简介',
    '生效时间': '简介',
    '废止时间': '简介',
}

# 关键字模糊匹配
def match_field(key):
    for k, v in FIELD_MAP.items():
        if k in key:
            return v
    return None

def extract_detail_fields_from_html(html, url, meta=None):
    """
    从详情页HTML中提取FIELDS字段，返回dict。
    meta: dict，包含来源站点、部门、级别等补充信息。
    """
    meta = meta or {}
    data = {k: '无' for k in FIELDS}
    data['详情地址'] = url
    for k in ['来源站点', '部门', '级别']:
        if k in meta:
            data[k] = meta[k]
    soup = BeautifulSoup(html, 'html.parser')
    # 1. 解析表格
    table = soup.find('table', class_='table_suoyin')
    if not table:
        # 如果没有找到 table_suoyin 类，尝试查找其他表格
        table = soup.find('table')
    if table:
        for row in table.find_all('tr'):
            cells = row.find_all(['th', 'td'])
            i = 0
            while i < len(cells) - 1:
                key = cells[i].get_text(strip=True).replace('\xa0', '').replace('\u3000', '').replace('&emsp;', '').replace(':', '').replace('：', '')
                val = cells[i+1].get_text(strip=True)
                field = match_field(key)
                if field:
                    # 多值合并
                    if data[field] == '无':
                        data[field] = val
                    else:
                        data[field] += ' ' + val
                i += 2
    # 2. 标题优先级：表格>meta>h1
    if data['标题'] == '无':
        if '标题' in meta:
            data['标题'] = meta['标题']
        else:
            h1 = soup.find(['h1'])
            if h1:
                data['标题'] = h1.get_text(strip=True)
    # 3. 正文内容 - 改进提取逻辑
    content = ''

    # 方法1：尝试从页面文本中提取正文
    page_text = soup.get_text()
    if data['标题'] and data['标题'] != '无':
        # 从标题开始提取到附件或扫一扫之前
        title_start = page_text.find(data['标题'])
        if title_start != -1:
            # 查找结束标记
            end_markers = ['相关附件：', '扫一扫在手机打开当前页', '责任编辑：']
            end_index = len(page_text)
            for marker in end_markers:
                marker_index = page_text.find(marker, title_start)
                if marker_index != -1:
                    end_index = min(end_index, marker_index)

            content = page_text[title_start:end_index].strip()

    # 方法2：如果上面没有提取到内容，尝试从article标签提取
    if not content or len(content) < 100:
        article = soup.find('article')
        if article:
            content = article.get_text(separator='\n', strip=True)

    # 方法3：兜底方案，提取所有p标签
    if not content or len(content) < 100:
        ps = soup.find_all('p')
        content = '\n'.join([p.get_text(strip=True) for p in ps if p.get_text(strip=True)])

    data['正文内容'] = content or '无'
    # 4. 敏感数字
    sens = re.findall(r"[\d.]+\s*(?:元|%|％|万元|亿元)", data['正文内容'])
    data['敏感数字'] = ', '.join(sens) if sens else '无'
    # 5. 附件 - 改进附件检测逻辑
    attaches = []
    for a in soup.find_all('a', href=True):
        name = a.get_text(strip=True)
        href = a['href']
        # 检测文件扩展名
        if any(ext in href.lower() for ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar', '.wps']):
            if href.startswith('http'):
                full_href = href
            else:
                # 处理相对路径
                if href.startswith('/'):
                    full_href = 'https://jx.ah.gov.cn' + href
                else:
                    full_href = url.rsplit('/', 1)[0] + '/' + href.lstrip('/')
            attaches.append(f'{name} ({full_href})')
    data['附件'] = '; '.join(attaches) if attaches else '无'
    # 6. 文件名及链接
    if data['标题'] != '无':
        data['文件名及链接'] = f'=HYPERLINK("{url}", "{data["标题"]}")'
    # 7. 清洗所有字段
    for k in FIELDS:
        if not data[k] or str(data[k]).strip() == '':
            data[k] = '无'
        else:
            data[k] = str(data[k]).strip()
    return data

script_name = os.path.splitext(os.path.basename(__file__))[0]
print(f"脚本名: {script_name}")
site_info = get_site_config(script_name)
print(f"站点信息: {site_info}")
LIST_URL = site_info['url']
SOURCE_SITE = site_info.get('dept', site_info.get('来源站点', ''))
DEPT = site_info.get('dept', '')
LEVEL = site_info.get('level', '')
print(f"URL: {LIST_URL}")

CONCURRENCY = 5

logger = get_logger(script_name)

async def fetch_all_detail_urls(page):
    detail_urls = []
    page_num = 1
    while True:
        # 等待页面加载完成
        await page.wait_for_timeout(2000)

        # 查找所有文档链接 - 使用修正后的选择器
        links = await page.query_selector_all('a[href*="/public/6991/"]')
        logger.info(f"第{page_num}页发现{len(links)}条记录")

        stop_flag = False
        for link in links:
            href = await link.get_attribute('href')
            title = (await link.inner_text()).strip()

            # 过滤掉解读链接和机构职能目录
            if ('解读' in title or '图片解读' in title or '文字解读' in title or '机构职能目录' in title):
                continue

            # 查找同级或父级元素中的日期
            pub_date = ''
            try:
                # 尝试从页面文本中提取日期
                page_text = await page.evaluate('() => document.body.textContent')
                # 简单的日期匹配，可以根据实际页面结构调整
                import re
                date_matches = re.findall(r'(\d{4}-\d{2}-\d{2})', page_text)
                if date_matches:
                    pub_date = date_matches[0]  # 取第一个匹配的日期
            except:
                pub_date = ''

            logger.info(f"条目标题: {title} | 发布时间: {pub_date}")

            # 检查是否为当前年份数据
            current_year = str(datetime.now().year)
            if pub_date and current_year not in pub_date:
                logger.info(f'遇到非{current_year}年数据，提前终止采集。')
                stop_flag = True
                break

            if href:
                detail_urls.append((href, title, pub_date))

        logger.info(f"采集到的详情链接样本: {detail_urls[:3]}")

        if stop_flag or len(detail_urls) >= 10:  # 限制采集数量
            break

        # 查找下一页按钮
        next_btn = await page.query_selector('a:has-text("下一页")')
        if next_btn and await next_btn.is_enabled():
            await next_btn.click()
            page_num += 1
            logger.info(f'翻到第{page_num}页...')
            await page.wait_for_timeout(1500)
        else:
            break

    logger.info(f"共采集到{len(detail_urls)}条详情页URL")
    return detail_urls

async def extract_detail(page, url_title):
    url, list_title, list_pub_date = url_title if isinstance(url_title, tuple) and len(url_title) == 3 else (url_title[0], url_title[1], '')
    try:
        logger.info(f"采集详情页: {url}")
        await page.goto(url, wait_until="domcontentloaded")
        await page.wait_for_timeout(1000)
        html = await page.content()
        meta = {
            '来源站点': SOURCE_SITE,
            '部门': DEPT,
            '级别': LEVEL,
            '标题': list_title,
            '发布时间': list_pub_date
        }
        data = extract_detail_fields_from_html(html, url, meta)
        # 修正：主题优先用列表title，无则用详情页title
        if list_title:
            data['主题'] = list_title
        elif data.get('标题'):
            data['主题'] = data['标题']
        logger.info(f"详情页采集样本: {data}")
        return [data.get(f, '') for f in FIELDS]
    except Exception as e:
        logger.error(f'采集失败: {url}, 错误: {e}')
        logger.info(f'采集失败详情页HTML片段: {(await page.content())[:500]}')
        logger.exception(e)
        return ['' for _ in FIELDS]

async def gather_with_concurrency(n, tasks):
    semaphore = asyncio.Semaphore(n)
    async def sem_task(task):
        async with semaphore:
            return await task
    return await asyncio.gather(*(sem_task(task) for task in tasks))

async def main():
    print(f"启动采集任务: {script_name}, 入口: {LIST_URL}")
    logger.info(f"启动采集任务: {script_name}, 入口: {LIST_URL}")

    print("正在启动浏览器...")
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()

        print(f"正在访问页面: {LIST_URL}")
        await page.goto(LIST_URL)

        print("正在获取详情页链接...")
        detail_urls = await fetch_all_detail_urls(page)

        print(f"获取到 {len(detail_urls)} 个详情页链接")

        # 并发抓取详情页
        tasks = []
        for url_title in detail_urls:
            detail_page = await browser.new_page()
            tasks.append(extract_detail(detail_page, url_title))

        print("正在抓取详情页...")
        results = await gather_with_concurrency(CONCURRENCY, tasks)

        print(f"详情页采集完成，共{len(results)}条")
        logger.info(f"详情页采集完成，共{len(results)}条")

        print("正在保存结果...")
        out_path = save_result_xlsx(script_name, FIELDS, results)

        print(f'采集完成，已输出到{out_path}')
        logger.info(f'采集完成，已输出到{out_path}')

        await browser.close()

if __name__ == '__main__':
    print("开始运行 spider_101.py")
    try:
        asyncio.run(main())
        print("spider_101.py 运行完成")
    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()
