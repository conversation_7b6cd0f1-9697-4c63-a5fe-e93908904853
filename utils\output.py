import os
import openpyxl
from openpyxl.styles import Font, Border, Side

def save_result_xlsx(script_name, fields, results):
    result_dir = os.path.join(os.path.dirname(__file__), '../result', script_name)
    os.makedirs(result_dir, exist_ok=True)
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.append(fields)
    # 设置表头加粗、加边框
    bold_font = Font(bold=True)
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
    for col in range(1, len(fields) + 1):
        cell = ws.cell(row=1, column=col)
        cell.font = bold_font
        cell.border = thin_border
    ws.freeze_panes = 'A2'
    ws.auto_filter.ref = ws.dimensions
    for row in results:
        max_attach = 1
        if row[fields.index('附件')]:
            attachs = row[fields.index('附件')].split('\n')
            max_attach = len(attachs)
        else:
            attachs = ['']
        for i in range(max_attach):
            excel_row = []
            for idx, f in enumerate(fields):
                if f == '附件':
                    val = attachs[i] if i < len(attachs) else ''
                    excel_row.append(val)
                else:
                    excel_row.append(row[idx] if i == 0 else '')
            ws.append(excel_row)
        # 附件超链接
        for i, att in enumerate(attachs):
            if att.startswith('=HYPERLINK('):
                url_title = att[len('=HYPERLINK('):-1]
                url, title = url_title.split(', "')
                url = url.strip('"')
                title = title.strip('"')
                cell = ws.cell(row=ws.max_row - len(attachs) + 1 + i, column=fields.index('附件') + 1)
                cell.value = title
                cell.hyperlink = url
                cell.style = 'Hyperlink'
        # 文件名及链接超链接
        if row[fields.index('文件名及链接')].startswith('=HYPERLINK('):
            url_title = row[fields.index('文件名及链接')][len('=HYPERLINK('):-1]
            url, title = url_title.split(', "')
            url = url.strip('"')
            title = title.strip('"')
            cell = ws.cell(row=ws.max_row - len(attachs) + 1, column=fields.index('文件名及链接') + 1)
            cell.value = title
            cell.hyperlink = url
            cell.style = 'Hyperlink'
    out_path = os.path.join(result_dir, f'{script_name}.xlsx')
    wb.save(out_path)
    return out_path 