# 爬虫架构设计方案与计划

## 方案选型
采用插件式脚本分发 + FastAPI统一入口 + 线程池并发 + 本地日志 + LLM API调用。

## 目录结构
```
ptl_crawler/
├── main.py                # FastAPI统一入口
├── spider_manager.py      # 脚本调度与分发
├── llm_client.py          # 大模型API封装
├── config/
│   ├── sites.yaml         # 网站/部门/省份/参数配置
├── spiders/
│   ├── spider_001.py      # 每个网站一个脚本，命名规范
├── logs/
│   ├── spider_001.log     # 每个脚本独立日志
├── utils/
│   ├── logger.py          # 日志工具
│   └── thread_pool.py     # 线程池封装
├── requirements.txt
└── README.md
```

## 主要实现步骤
1. 配置管理：config/sites.yaml 维护所有网站、部门、省份、脚本映射关系。
2. 统一入口（main.py）：FastAPI实现REST接口，支持单个/批量任务提交。
3. 脚本调度（spider_manager.py）：动态加载 spiders/ 下脚本，多线程并发调度。
4. 爬虫脚本（spiders/）：每个网站一个脚本，统一接口，调用llm_client.py。
5. 日志管理（utils/logger.py）：每个脚本独立日志。
6. 大模型API（llm_client.py）：封装大模型API。
7. 多线程并发（utils/thread_pool.py）：封装ThreadPoolExecutor。
8. 文档与依赖：README.md、requirements.txt。

## 预期结果
- 支持通过API按省份/部门/网站ID批量调度采集任务。
- 每个采集脚本独立日志。
- 每个脚本可调用大模型API做正文总结。
- 多线程并发，提升采集效率。
- 易于扩展新网站脚本。

## 依赖库
- FastAPI
- loguru 或 logging
- requests/selenium
- concurrent.futures
- pyyaml
- openai/llm相关库 